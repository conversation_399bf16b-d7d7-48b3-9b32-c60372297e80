hoistPattern:
  - '*'
hoistedDependencies:
  '@cloudflare/kv-asset-handler@0.4.0':
    '@cloudflare/kv-asset-handler': private
  '@cloudflare/unenv-preset@2.5.0(unenv@2.0.0-rc.19)(workerd@1.20250726.0)':
    '@cloudflare/unenv-preset': private
  '@cloudflare/workerd-darwin-64@1.20250726.0':
    '@cloudflare/workerd-darwin-64': private
  '@cloudflare/workerd-darwin-arm64@1.20250726.0':
    '@cloudflare/workerd-darwin-arm64': private
  '@cloudflare/workerd-linux-64@1.20250726.0':
    '@cloudflare/workerd-linux-64': private
  '@cloudflare/workerd-linux-arm64@1.20250726.0':
    '@cloudflare/workerd-linux-arm64': private
  '@cloudflare/workerd-windows-64@1.20250726.0':
    '@cloudflare/workerd-windows-64': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@esbuild/aix-ppc64@0.25.4':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.4':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.4':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.4':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.4':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.4':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.4':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.4':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.4':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.4':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.4':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.4':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.4':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.4':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.4':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.4':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.4':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.4':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.4':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.4':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.4':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.4':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.4':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.4':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.4':
    '@esbuild/win32-x64': private
  '@img/sharp-darwin-arm64@0.33.5':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.33.5':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.0.4':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.0.4':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.0.4':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.0.5':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-s390x@1.0.4':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.0.4':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.0.4':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.33.5':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.33.5':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.33.5':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.33.5':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.33.5':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.33.5':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.33.5':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-ia32@0.33.5':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.33.5':
    '@img/sharp-win32-x64': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@poppinss/colors@4.1.5':
    '@poppinss/colors': private
  '@poppinss/dumper@0.6.4':
    '@poppinss/dumper': private
  '@poppinss/exception@1.2.2':
    '@poppinss/exception': private
  '@sindresorhus/is@7.0.2':
    '@sindresorhus/is': private
  '@speed-highlight/core@1.2.7':
    '@speed-highlight/core': private
  acorn-walk@8.3.2:
    acorn-walk: private
  acorn@8.14.0:
    acorn: private
  blake3-wasm@2.1.5:
    blake3-wasm: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  cookie@1.0.2:
    cookie: private
  defu@6.1.4:
    defu: private
  detect-libc@2.0.4:
    detect-libc: private
  error-stack-parser-es@1.0.5:
    error-stack-parser-es: private
  esbuild@0.25.4:
    esbuild: private
  exit-hook@2.2.1:
    exit-hook: private
  exsolve@1.0.7:
    exsolve: private
  fsevents@2.3.3:
    fsevents: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  is-arrayish@0.3.2:
    is-arrayish: private
  kleur@4.1.5:
    kleur: private
  mime@3.0.0:
    mime: private
  miniflare@4.20250726.0:
    miniflare: private
  ohash@2.0.11:
    ohash: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  pathe@2.0.3:
    pathe: private
  semver@7.7.2:
    semver: private
  sharp@0.33.5:
    sharp: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  stoppable@1.1.0:
    stoppable: private
  supports-color@10.0.0:
    supports-color: private
  ufo@1.6.1:
    ufo: private
  undici@7.12.0:
    undici: private
  unenv@2.0.0-rc.19:
    unenv: private
  workerd@1.20250726.0:
    workerd: private
  ws@8.18.0:
    ws: private
  youch-core@0.3.3:
    youch-core: private
  youch@4.1.0-beta.10:
    youch: private
  zod@3.22.3:
    zod: private
ignoredBuilds:
  - workerd
  - esbuild
  - sharp
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.10.0
pendingBuilds: []
prunedAt: Thu, 31 Jul 2025 01:43:32 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@cloudflare/workerd-darwin-64@1.20250726.0'
  - '@cloudflare/workerd-darwin-arm64@1.20250726.0'
  - '@cloudflare/workerd-linux-64@1.20250726.0'
  - '@cloudflare/workerd-linux-arm64@1.20250726.0'
  - '@emnapi/runtime@1.4.5'
  - '@esbuild/aix-ppc64@0.25.4'
  - '@esbuild/android-arm64@0.25.4'
  - '@esbuild/android-arm@0.25.4'
  - '@esbuild/android-x64@0.25.4'
  - '@esbuild/darwin-arm64@0.25.4'
  - '@esbuild/darwin-x64@0.25.4'
  - '@esbuild/freebsd-arm64@0.25.4'
  - '@esbuild/freebsd-x64@0.25.4'
  - '@esbuild/linux-arm64@0.25.4'
  - '@esbuild/linux-arm@0.25.4'
  - '@esbuild/linux-ia32@0.25.4'
  - '@esbuild/linux-loong64@0.25.4'
  - '@esbuild/linux-mips64el@0.25.4'
  - '@esbuild/linux-ppc64@0.25.4'
  - '@esbuild/linux-riscv64@0.25.4'
  - '@esbuild/linux-s390x@0.25.4'
  - '@esbuild/linux-x64@0.25.4'
  - '@esbuild/netbsd-arm64@0.25.4'
  - '@esbuild/netbsd-x64@0.25.4'
  - '@esbuild/openbsd-arm64@0.25.4'
  - '@esbuild/openbsd-x64@0.25.4'
  - '@esbuild/sunos-x64@0.25.4'
  - '@esbuild/win32-arm64@0.25.4'
  - '@esbuild/win32-ia32@0.25.4'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-win32-ia32@0.33.5'
  - fsevents@2.3.3
  - tslib@2.8.1
storeDir: C:\Users\<USER>\AppData\Roaming\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\code\verify\backend\node_modules\.pnpm
virtualStoreDirMaxLength: 60
