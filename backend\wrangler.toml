name = "license-verify-backend"
main = "src/index.ts"
compatibility_date = "2025-07-31"

[env.production]
name = "license-verify-backend"

[env.development]
name = "license-verify-backend-dev"

# D1 数据库配置
[[d1_databases]]
binding = "DB"
database_name = "license_verify_db"
database_id = ""

# KV 存储配置
[[kv_namespaces]]
binding = "CACHE"
id = ""
preview_id = ""

# 环境变量
[vars]
JWT_SECRET = "your-jwt-secret-here"
API_VERSION = "v1"

# CORS 配置
[env.production.vars]
ALLOWED_ORIGINS = "https://your-frontend-domain.pages.dev"

[env.development.vars]
ALLOWED_ORIGINS = "*"
