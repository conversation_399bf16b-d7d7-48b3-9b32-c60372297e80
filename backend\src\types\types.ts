export interface CloudflareBindings {
  DB: D1Database;
  CACHE: KVNamespace;
  JWT_SECRET: string;
  API_VERSION: string;
  ALLOWED_ORIGINS: string;
}

export interface Admin {
  id: number;
  username: string;
  password_hash: string;
  role: 'super' | 'normal';
  status: 'active' | 'inactive';
  product_ids?: string;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: number;
  name: string;
  description?: string;
  verification_strategy: 'expiration' | 'device_count' | 'feature_based';
  max_devices?: number;
  features?: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface License {
  id: number;
  product_id: number;
  license_key: string;
  status: 'active' | 'expired' | 'revoked';
  expires_at?: string;
  max_devices?: number;
  admin_id: number;
  created_at: string;
  updated_at: string;
}

export interface Device {
  id: number;
  license_id: number;
  device_id: string;
  device_info?: string;
  last_verification: string;
  created_at: string;
  updated_at: string;
}

export interface VerificationLog {
  id: number;
  license_key: string;
  device_id?: string;
  result: 'success' | 'failed';
  reason?: string;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export interface Order {
  id: number;
  admin_id: number;
  product_id: number;
  license_count: number;
  unit_price: number;
  total_price: number;
  status: 'pending' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

export interface VerifyRequest {
  license_key: string;
  device_id?: string;
  product_features?: string[];
}

export interface VerifyResponse {
  success: boolean;
  message: string;
  license_info?: {
    product_name: string;
    expires_at?: string;
    max_devices?: number;
    current_devices: number;
    features?: string[];
  };
}

export interface AdminLoginRequest {
  username: string;
  password: string;
}

export interface AdminLoginResponse {
  success: boolean;
  message: string;
  token?: string;
  refresh_token?: string;
  admin_info?: {
    id: number;
    username: string;
    role: string;
    product_ids?: string[];
  };
}

export interface CreateProductRequest {
  name: string;
  description?: string;
  verification_strategy: 'expiration' | 'device_count' | 'feature_based';
  max_devices?: number;
  features?: string[];
}

export interface CreateLicenseRequest {
  product_id: number;
  count: number;
  expires_at?: string;
  max_devices?: number;
}

export interface CreateAdminRequest {
  username: string;
  password: string;
  role: 'super' | 'normal';
  product_ids?: number[];
}

export interface UnbindDeviceRequest {
  license_key: string;
  device_id: string;
}

export interface ApiError {
  success: false;
  message: string;
  code?: string;
}

export interface ApiSuccess<T = any> {
  success: true;
  message: string;
  data?: T;
}

export type ApiResponse<T = any> = ApiSuccess<T> | ApiError;

export interface JWTPayload {
  admin_id: number;
  username: string;
  role: string;
  exp: number;
  iat: number;
}

export interface CacheKeys {
  licenseVerify: (key: string) => string;
  deviceBinding: (licenseId: number) => string;
  productConfig: (id: number) => string;
  adminAuth: (id: number) => string;
  salesStats: (adminId: number, period: string) => string;
}