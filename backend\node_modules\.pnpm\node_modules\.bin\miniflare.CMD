@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\code\verify\backend\node_modules\.pnpm\miniflare@4.20250726.0\node_modules\miniflare\node_modules;C:\Users\<USER>\code\verify\backend\node_modules\.pnpm\miniflare@4.20250726.0\node_modules;C:\Users\<USER>\code\verify\backend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\code\verify\backend\node_modules\.pnpm\miniflare@4.20250726.0\node_modules\miniflare\node_modules;C:\Users\<USER>\code\verify\backend\node_modules\.pnpm\miniflare@4.20250726.0\node_modules;C:\Users\<USER>\code\verify\backend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\miniflare\bootstrap.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\miniflare\bootstrap.js" %*
)
