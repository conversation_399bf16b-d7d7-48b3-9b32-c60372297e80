# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a software license verification service system built on Cloudflare Workers. It provides license validation services for individual software developers and supports multi-level administrator management with sales statistics dashboards.

## Architecture

- **Backend**: Cloudflare Workers using Hono framework (TypeScript)
- **Database**: Cloudflare D1 (SQLite) for primary data storage
- **Cache**: Cloudflare KV for high-frequency verification requests
- **Deployment**: Wrangler for deployment to Cloudflare Workers

## Development Commands

Navigate to the `backend/` directory for all development commands:

```bash
cd backend
```

### Core Commands

- `pnpm run dev` - Start development server with hot reloading
- `pnpm run deploy` - Deploy to Cloudflare Workers with minification
- `pnpm run cf-typegen` - Generate TypeScript types for Cloudflare bindings

### Database Commands

```bash
# Create D1 database
wrangler d1 create verify-db

# Create KV namespace
wrangler kv:namespace create "VERIFY_CACHE"
wrangler kv:namespace create "VERIFY_CACHE" --preview
```

## Configuration

### Wrangler Configuration

The `wrangler.toml` file in the backend directory contains:

- D1 database bindings (`DB`)
- KV namespace bindings (`CACHE`)
- Environment-specific configurations (development/production)
- JWT secrets and API versions
- CORS settings

### Environment Variables

- `JWT_SECRET` - JWT signing secret
- `API_VERSION` - API version (v1)
- `ALLOWED_ORIGINS` - CORS allowed origins (configurable per environment)

## Database Schema

### Primary Tables (D1)

- `admins` - Administrator accounts with role-based permissions
- `products` - Software products with verification strategies
- `licenses` - License keys with expiration and device limits
- `devices` - Device binding records for license validation
- `verification_logs` - Verification request audit trail
- `orders` - Order management for sales tracking

### Cache Layer (KV)

- License verification cache (`license:verify:{key}`)
- Device binding cache (`device:binding:{license_id}`)
- Product configuration cache (`product:config:{id}`)
- Admin authentication cache (`admin:auth:{id}`)
- Sales statistics cache (`stats:{admin_id}:{period}`)

## API Structure

### Client APIs (Public)

- `POST /verify` - License verification endpoint
- `POST /device/unbind` - Device unbinding

### Admin APIs (Authenticated)

- Authentication: `POST /admin/login`, `POST /admin/refresh`
- Product management: CRUD operations (super admin only)
- License management: Generate, list, revoke, view details
- Administrator management: Create/manage distributors (super admin only)
- Statistics: Sales and usage analytics
- Order management: View and update order status

## Business Logic

### User Roles

- **Super Admin**: Full system access, product management, distributor creation
- **Normal Admin**: Limited to assigned products, license generation, personal statistics

### Verification Strategies

- Expiration date validation
- Device count limitations
- Feature-based permissions
- Configurable per product

### Data Flow

1. License verification requests hit KV cache first
2. Cache misses query D1 database
3. Management operations update D1 then sync to KV
4. Statistics calculated from order data with caching

## TypeScript Configuration

The project uses modern TypeScript with:

- ESNext target and modules
- Bundler module resolution
- Strict type checking enabled
- Hono JSX integration
- Cloudflare Workers types via `wrangler types`

## Key Integration Points

When implementing features:

1. Use `CloudflareBindings` type for database/KV access
2. Follow the established cache-first verification pattern
3. Maintain D1 ↔ KV synchronization for data consistency
4. Implement proper role-based authorization checks
5. Log all verification attempts for audit purposes
